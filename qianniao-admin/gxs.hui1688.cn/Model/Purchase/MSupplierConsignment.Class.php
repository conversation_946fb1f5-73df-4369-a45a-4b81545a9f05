<?php
/**
 * 供应商代销配置模型
 * 处理供应商代销配置和相关业务逻辑
 */

namespace Jin<PERSON>ou<PERSON><PERSON>\Model\Purchase;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\Common\Logger;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Purchase\DSupplier;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Dao\Consignment\DConsignmentRule;
use <PERSON>DouYun\Model\MBaseModel;
use Mall\Framework\Core\ErrorCode;
use Mall\Framework\Core\ResultWrapper;
use Mall\Framework\Core\StatusCode;

class MSupplierConsignment extends MBaseModel
{
    /**
     * 当前企业ID
     * @var int
     */
    protected $onlineEnterpriseId;

    /**
     * 当前用户ID
     * @var int
     */
    protected $onlineUserId;

    /**
     * 供应商数据访问对象
     * @var DSupplier
     */
    protected $objDSupplier;

    /**
     * 分账规则数据访问对象
     * @var DConsignmentRule
     */
    protected $objDConsignmentRule;

    /**
     * 构造函数
     *
     * @param int $onlineEnterpriseId 当前企业ID
     * @param int $onlineUserId 当前用户ID
     */
    public function __construct($onlineEnterpriseId, $onlineUserId)
    {
        parent::__construct($onlineEnterpriseId, $onlineUserId);
        $this->onlineEnterpriseId = $onlineEnterpriseId;
        $this->onlineUserId = $onlineUserId;

        // 初始化数据访问对象
        $this->objDSupplier = new DSupplier();
        $this->objDSupplier->setTable('qianniao_supplier_' . $this->onlineEnterpriseId);

        $this->objDConsignmentRule = new DConsignmentRule();
        $this->objDConsignmentRule->setTable('qianniao_consignment_rule_' . $this->onlineEnterpriseId);
    }

    /**
     * 获取供应商代销配置
     *
     * @param int $supplierId 供应商ID
     * @return ResultWrapper 结果包装器，成功时返回供应商代销配置信息，失败时返回错误信息
     */
    public function getConsignmentConfig($supplierId): ResultWrapper
    {
        try {
            // 参数验证
            if (empty($supplierId)) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            // 获取供应商信息
            $supplier = $this->objDSupplier->get($supplierId, 'id, title, consignmentConfig');
            if ($supplier === false) {
                Logger::logs(E_USER_ERROR, '获取供应商代销配置失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            if (empty($supplier)) {
                return ResultWrapper::fail('供应商不存在', ErrorCode::$paramError);
            }

            // 解析配置
            $config = [];
            if (!empty($supplier['consignmentConfig'])) {
                $config = json_decode($supplier['consignmentConfig'], true);
            }

            // 设置默认值并构建返回数据
            $result = [
                'supplierId' => $supplier['id'],
                'supplierName' => $supplier['title'],
                'enabled' => isset($config['enabled']) ? (bool)$config['enabled'] : false,
                'depositRequired' => isset($config['depositRequired']) ? (bool)$config['depositRequired'] : false,
                'minDepositAmount' => isset($config['minDepositAmount']) ? floatval($config['minDepositAmount']) : 1000.0,
                'settlementConfig' => isset($config['settlementConfig']) ? $config['settlementConfig'] : null
            ];

            return ResultWrapper::success($result);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取供应商代销配置异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('获取供应商代销配置失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 更新供应商代销配置
     *
     * @param int $supplierId 供应商ID
     * @param array $configData 配置数据
     * @return ResultWrapper 结果包装器，成功时返回供应商ID，失败时返回错误信息
     */
    public function updateConsignmentConfig($supplierId, $configData): ResultWrapper
    {
        try {
            // 参数验证
            if (empty($supplierId)) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            if (empty($configData) || !is_array($configData)) {
                return ResultWrapper::fail('配置数据不能为空', ErrorCode::$paramError);
            }

            // 获取供应商信息
            $supplier = $this->objDSupplier->get($supplierId);
            if ($supplier === false) {
                Logger::logs(E_USER_ERROR, '获取供应商信息失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            if (empty($supplier)) {
                return ResultWrapper::fail('供应商不存在', ErrorCode::$paramError);
            }

            // 获取现有配置
            $existingConfig = [];
            if (!empty($supplier['consignmentConfig'])) {
                $existingConfig = json_decode($supplier['consignmentConfig'], true);
            }

            // 合并配置
            $newConfig = array_merge($existingConfig, $configData);

            // 更新供应商表
            $updateData = [
                'consignmentConfig' => json_encode($newConfig),
                'updateTime' => time()
            ];

            $result = $this->objDSupplier->update($updateData, $supplierId);
            if ($result === false) {
                Logger::logs(E_USER_ERROR, '更新供应商代销配置失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            return ResultWrapper::success(['supplierId' => $supplierId]);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '更新供应商代销配置异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('更新供应商代销配置失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取代销供应商列表
     *
     * @param array $conditions 查询条件，包含keyword、supplierId、enabled等
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return ResultWrapper 结果包装器，成功时返回供应商列表和总数，失败时返回错误信息
     */
    public function getConsignmentSuppliers($conditions, $page, $pageSize): ResultWrapper
    {
        try {
            // 参数验证
            if (!is_array($conditions)) {
                $conditions = [];
            }

            $page = max(1, intval($page));
            $pageSize = max(1, intval($pageSize));

            // 构建查询条件
            $where = ['deleteStatus' => StatusCode::$standard];

            // 关键词搜索
            if (!empty($conditions['keyword'])) {
                $keyword = $conditions['keyword'];
                // 使用数组形式构建查询条件，避免SQL注入
                $where[] = [
                    'OR' => [
                        ['title', 'like', '%' . $keyword . '%'],
                        ['code', 'like', '%' . $keyword . '%']
                    ]
                ];
            }

            // 指定供应商ID
            if (!empty($conditions['supplierId'])) {
                $where['id'] = intval($conditions['supplierId']);
            }

            // 代销状态
            if (isset($conditions['enabled'])) {
                // 通过consignmentConfig字段中的enabled属性判断
                $this->objDSupplier->addWhereRaw("JSON_EXTRACT(consignmentConfig, '$.enabled') = " . ($conditions['enabled'] ? 'true' : 'false'));
            }

            // 计算分页参数
            $limit = $pageSize;
            $offset = ($page - 1) * $pageSize;

            // 使用SqlHelper方法进行查询
            // 查询总数
            $total = $this->objDSupplier->count($where);
            if ($total === false) {
                Logger::logs(E_USER_ERROR, '获取代销供应商总数失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            // 查询数据，包含保证金账户余额字段
            $fields = 'id as supplierId, title, code, mobile, realName, consignmentConfig, depositAccount, createTime, updateTime';
            $suppliers = $this->objDSupplier->select($where, $fields, 'updateTime DESC', $limit, $offset);

            if ($suppliers === false) {
                Logger::logs(E_USER_ERROR, '获取代销供应商列表失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            // 处理结果
            $list = [];
            foreach ($suppliers as $supplier) {
                $config = !empty($supplier['consignmentConfig']) ? json_decode($supplier['consignmentConfig'], true) : [];
                $list[] = [
                    'supplierId' => $supplier['supplierId'],
                    'title' => $supplier['title'],
                    'code' => $supplier['code'],
                    'mobile' => $supplier['mobile'],
                    'realName' => $supplier['realName'] ?? '',
                    'depositAccount' => floatval($supplier['depositAccount'] ?? 0),
                    'status' => isset($config['enabled']) ? (bool)$config['enabled'] : false,
                    'createTime' => $supplier['createTime'],
                    'updateTime' => $supplier['updateTime']
                ];
            }

            return ResultWrapper::success([
                'list' => $list,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取代销供应商列表异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('获取代销供应商列表失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取代销状态统计
     *
     * @return ResultWrapper 结果包装器，成功时返回统计数据，失败时返回错误信息
     */
    public function getConsignmentStats(): ResultWrapper
    {
        try {
            // 使用SqlHelper方法进行查询
            // 查询总供应商数
            $whereTotal = ['deleteStatus' => StatusCode::$standard];
            $total = $this->objDSupplier->count($whereTotal);
            if ($total === false) {
                Logger::logs(E_USER_ERROR, '获取供应商总数失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            // 查询启用代销的供应商数
            $whereEnabled = [
                'deleteStatus' => StatusCode::$standard
            ];
            // 使用JSON_EXTRACT查询启用代销的供应商
            $this->objDSupplier->addWhereRaw("JSON_EXTRACT(consignmentConfig, '$.enabled') = true");
            $enabled = $this->objDSupplier->count($whereEnabled);
            if ($enabled === false) {
                Logger::logs(E_USER_ERROR, '获取启用代销的供应商数失败', __CLASS__, __LINE__, $this->objDSupplier->error());
                return ResultWrapper::fail($this->objDSupplier->error(), ErrorCode::$dberror);
            }

            // 返回统计结果
            return ResultWrapper::success([
                'total' => $total,
                'enabled' => $enabled,
                'disabled' => $total - $enabled
            ]);
        } catch (\Exception $e) {
            Logger::logs(E_USER_ERROR, '获取代销状态统计异常', __CLASS__, __LINE__, $e->getMessage());
            return ResultWrapper::fail('获取代销状态统计失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取供应商概览数据（供应商角色端专用）
     * 任务3：供应商角色概览页
     *
     * @param int $supplierId 供应商ID
     * @return ResultWrapper 概览数据
     */
    public function getSupplierOverviewData($supplierId): ResultWrapper
    {
        try {
            // 验证必要参数
            if (empty($supplierId)) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            // 获取供应商基本信息
            $supplier = $this->objDSupplier->getSupplierById($supplierId);
            if ($supplier === false) {
                return ResultWrapper::fail('获取供应商信息失败：' . $this->objDSupplier->error(), ErrorCode::$dberror);
            }

            if (empty($supplier)) {
                return ResultWrapper::fail('供应商不存在', ErrorCode::$paramError);
            }

            // 获取代销配置
            $config = [];
            if (!empty($supplier['consignmentConfig'])) {
                $config = json_decode($supplier['consignmentConfig'], true);
            }

            // 获取统计数据
            $statistics = $this->getSupplierStatistics($supplierId);

            // 构建概览数据
            $overviewData = [
                'supplierInfo' => [
                    'id' => $supplier['id'],
                    'title' => $supplier['title'],
                    'code' => $supplier['code'],
                    'mobile' => $supplier['mobile'],
                    'createTime' => $supplier['createTime'],
                ],
                'consignmentStatus' => [
                    'enabled' => isset($config['enabled']) ? (bool)$config['enabled'] : false,
                ],
                'depositAccount' => [
                    'balance' => floatval($supplier['depositAccount'] ?? 0),
                ],
                'statistics' => $statistics
            ];

            return ResultWrapper::success($overviewData);
        } catch (\Exception $e) {
            return ResultWrapper::fail('获取供应商概览数据失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 获取供应商统计数据（供应商角色端专用）
     * 任务3：供应商角色概览页
     *
     * @param int $supplierId 供应商ID
     * @return array 统计数据
     */
    private function getSupplierStatistics($supplierId): array
    {
        try {
            // 初始化统计数据
            $statistics = [
                'totalInventory' => 0,      // 当前库存总量
                'monthlyInbound' => 0,      // 本月入库数量
                'monthlyOutbound' => 0,     // 本月出库数量
                'pendingSettlement' => 0,   // 待结算金额
            ];

            // 获取当前库存总量
            $statistics['totalInventory'] = $this->getCurrentInventoryTotal($supplierId);

            // 获取本月入库出库统计
            $monthStats = $this->getMonthlyStorageStats($supplierId);
            $statistics['monthlyInbound'] = $monthStats['inbound'];
            $statistics['monthlyOutbound'] = $monthStats['outbound'];

            // 获取待结算金额
            $statistics['pendingSettlement'] = $this->getPendingSettlementAmount($supplierId);

            return $statistics;
        } catch (\Exception $exception) {
            // 出现异常时返回默认值，记录日志
            Logger::logs(E_USER_WARNING, '获取供应商概览数据失败', __CLASS__, __LINE__, $exception->getMessage());
            return [
                'totalInventory' => 0,
                'monthlyInbound' => 0,
                'monthlyOutbound' => 0,
                'pendingSettlement' => 0,
            ];
        }
    }

    /**
     * 获取当前库存总量
     *
     * @param int $supplierId 供应商ID
     * @return int 库存总量
     */
    private function getCurrentInventoryTotal($supplierId): int
    {
        try {
            // 使用SQL查询获取供应商当前库存总量
            $sql = "SELECT SUM(num) as total FROM qianniao_inventory_supplier_" . $this->onlineEnterpriseId . " WHERE supplierId = " . intval($supplierId);
            $result = $this->objDSupplier->query($sql);

            if ($result && !empty($result[0]['total'])) {
                return intval($result[0]['total']);
            }

            return 0;
        } catch (\Exception $exception) {
            Logger::logs(E_USER_WARNING, '获取库存总量失败', __CLASS__, __LINE__, $exception->getMessage());
            return 0;
        }
    }

    /**
     * 获取本月入库出库统计
     *
     * @param int $supplierId 供应商ID
     * @return array 本月统计数据
     */
    private function getMonthlyStorageStats($supplierId): array
    {
        try {
            // 计算本月开始和结束时间戳
            $monthStart = strtotime(date('Y-m-01 00:00:00'));
            $monthEnd = strtotime(date('Y-m-t 23:59:59'));

            // 查询本月入库数量
            $inboundSql = "SELECT SUM(num) as total FROM qianniao_inventory_supplier_details_" . $this->onlineEnterpriseId .
                         " WHERE supplierId = " . intval($supplierId) .
                         " AND actionType = 'inbound' AND createTime >= " . $monthStart . " AND createTime <= " . $monthEnd;
            $inboundResult = $this->objDSupplier->query($inboundSql);
            $inboundTotal = ($inboundResult && !empty($inboundResult[0]['total'])) ? intval($inboundResult[0]['total']) : 0;

            // 查询本月出库数量
            $outboundSql = "SELECT SUM(num) as total FROM qianniao_inventory_supplier_details_" . $this->onlineEnterpriseId .
                          " WHERE supplierId = " . intval($supplierId) .
                          " AND actionType = 'outbound' AND createTime >= " . $monthStart . " AND createTime <= " . $monthEnd;
            $outboundResult = $this->objDSupplier->query($outboundSql);
            $outboundTotal = ($outboundResult && !empty($outboundResult[0]['total'])) ? intval($outboundResult[0]['total']) : 0;

            return [
                'inbound' => $inboundTotal,
                'outbound' => $outboundTotal
            ];
        } catch (\Exception $exception) {
            Logger::logs(E_USER_WARNING, '获取流转统计失败', __CLASS__, __LINE__, $exception->getMessage());
            return [
                'inbound' => 0,
                'outbound' => 0
            ];
        }
    }

    /**
     * 获取待结算金额
     *
     * @param int $supplierId 供应商ID
     * @return float 待结算金额
     */
    private function getPendingSettlementAmount($supplierId): float
    {
        try {
            // 查询待结算金额（这里需要根据实际的结算表结构来调整）
            // 暂时返回0，后续在结算模块完成后再完善
            // TODO: 实现根据 $supplierId 查询待结算金额的逻辑
            unset($supplierId); // 临时处理未使用变量警告
            return 0.0;
        } catch (\Exception $exception) {
            Logger::logs(E_USER_WARNING, '获取待结算金额失败', __CLASS__, __LINE__, $exception->getMessage());
            return 0.0;
        }
    }

    /**
     * 获取固定价格列表（供应商角色端专用）
     * 任务7：固定价格查询模块 - 后端
     *
     * @param int $supplierId 供应商ID
     * @param array $page 分页参数
     * @param array $searchParams 搜索参数
     * @return ResultWrapper 固定价格列表
     */
    public function getSupplierFixedPriceList($supplierId, $page, $searchParams = []): ResultWrapper
    {
        try {
            // 验证必要参数
            if (empty($supplierId)) {
                return ResultWrapper::fail('供应商ID不能为空', ErrorCode::$paramError);
            }

            // 获取供应商代销配置
            $configResult = $this->objDSupplier->getConsignmentConfigBySupplierId($supplierId);
            if ($configResult === false) {
                return ResultWrapper::fail('获取代销配置失败：' . $this->objDSupplier->error(), ErrorCode::$dberror);
            }

            if (empty($configResult)) {
                return ResultWrapper::success([
                    'list' => [],
                    'total' => 0
                ]);
            }

            // 解析代销配置中的固定价格信息
            $config = [];
            if (!empty($configResult['consignmentConfig'])) {
                $config = json_decode($configResult['consignmentConfig'], true);
            }

            // 获取固定价格列表
            $priceList = [];
            if (isset($config['fixedPrices']) && is_array($config['fixedPrices'])) {
                $priceList = $config['fixedPrices'];
            }

            // 应用搜索过滤
            $filteredList = $this->filterFixedPriceList($priceList, $searchParams);

            // 分页处理
            $total = count($filteredList);
            $offset = $page['offset'] * $page['limit'];
            $pagedList = array_slice($filteredList, $offset, $page['limit']);

            // 格式化数据
            $formattedList = $this->formatFixedPriceList($pagedList);

            return ResultWrapper::success([
                'list' => $formattedList,
                'total' => $total
            ]);
        } catch (\Exception $e) {
            return ResultWrapper::fail('获取固定价格列表失败：' . $e->getMessage(), ErrorCode::$systemError);
        }
    }

    /**
     * 过滤固定价格列表数据
     *
     * @param array $priceList 原始价格数据
     * @param array $searchParams 搜索参数
     * @return array 过滤后的价格数据
     */
    private function filterFixedPriceList($priceList, $searchParams)
    {
        if (empty($searchParams)) {
            return $priceList;
        }

        $filteredList = [];
        $keyword = $searchParams['keyword'] ?? '';
        $status = $searchParams['status'] ?? '';

        foreach ($priceList as $price) {
            $match = true;

            // 关键词搜索（商品名称或SKU）
            if (!empty($keyword)) {
                $goodsName = $price['goodsName'] ?? '';
                $sku = $price['sku'] ?? '';
                $goodsCode = $price['goodsCode'] ?? '';

                if (stripos($goodsName, $keyword) === false &&
                    stripos($sku, $keyword) === false &&
                    stripos($goodsCode, $keyword) === false) {
                    $match = false;
                }
            }

            // 状态筛选
            if (!empty($status) && $match) {
                $priceStatus = $price['status'] ?? 'active';
                if ($priceStatus !== $status) {
                    $match = false;
                }
            }

            if ($match) {
                $filteredList[] = $price;
            }
        }

        return $filteredList;
    }

    /**
     * 格式化固定价格列表数据
     *
     * @param array $priceList 原始价格数据
     * @return array 格式化后的价格数据
     */
    private function formatFixedPriceList($priceList)
    {
        $formattedList = [];

        foreach ($priceList as $index => $price) {
            $formattedPrice = [
                'id' => $index + 1,
                'goodsName' => $price['goodsName'] ?? '',
                'goodsCode' => $price['goodsCode'] ?? '',
                'sku' => $price['sku'] ?? '',
                'fixedPrice' => floatval($price['fixedPrice'] ?? 0),
                'effectiveTime' => $price['effectiveTime'] ?? '',
                'status' => $price['status'] ?? 'active',
                'statusName' => ($price['status'] ?? 'active') === 'active' ? '生效中' : '已失效',
                'createTime' => $price['createTime'] ?? time(),
                'createTimeFormat' => date('Y-m-d H:i:s', $price['createTime'] ?? time()),
                'remark' => $price['remark'] ?? '',
            ];

            $formattedList[] = $formattedPrice;
        }

        return $formattedList;
    }
}
