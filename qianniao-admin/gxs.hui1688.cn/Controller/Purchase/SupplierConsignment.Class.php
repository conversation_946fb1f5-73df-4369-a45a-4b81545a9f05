<?php
/**
 * 供应商代销配置控制器
 * 处理供应商代销配置和保证金账户相关API请求
 */

namespace JinDou<PERSON>un\Controller\Purchase;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Controller\BaseController;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Purchase\MSupplier;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Purchase\MSupplierDeposit;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Purchase\MSupplierConsignment;
use Jin<PERSON><PERSON><PERSON>un\Model\Finance\MSettlementCycle;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Finance\MConsignmentSettlementDetail;
use JinDouYun\Model\Order\MOrder;
use JinDouYun\Model\Stock\MInventoryOut;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Model\Stock\MInventory;
use Jin<PERSON><PERSON><PERSON><PERSON>\Model\Stock\MInventorySupplier;
use Mall\Framework\Core\ErrorCode;

class SupplierConsignment extends BaseController
{
    /**
     * 供应商模型
     * @var MSupplier
     */
    protected $objMSupplier;

    /**
     * 供应商保证金模型
     * @var MSupplierDeposit
     */
    protected $objMSupplierDeposit;

    /**
     * 结算周期模型
     * @var MSettlementCycle
     */
    protected $objMSettlementCycle;

    /**
     * 订单模型
     * @var MOrder
     */
    protected $objMOrder;

    /**
     * 出库单模型
     * @var MInventoryOut
     */
    protected $objMInventoryOut;



    /**
     * 分账明细模型
     * @var MConsignmentSettlementDetail
     */
    protected $objMConsignmentSettlementDetail;



    /**
     * 供应商代销配置模型
     * @var MSupplierConsignment
     */
    protected $objMSupplierConsignment;

    /**
     * 构造函数
     *
     * @param bool $isCheckAcl 是否检查权限
     * @param bool $isMustLogin 是否必须登录
     */
    public function __construct($isCheckAcl = true, $isMustLogin = true)
    {
        parent::__construct($isCheckAcl, $isMustLogin);
        $this->objMSupplier = new MSupplier($this->onlineUserId, $this->onlineEnterpriseId);
        $this->objMSupplierDeposit = new MSupplierDeposit($this->onlineEnterpriseId, $this->onlineUserId);
        $this->objMSettlementCycle = new MSettlementCycle($this->onlineEnterpriseId, $this->onlineUserId);

        // 初始化订单和出库单模型
        $this->objMOrder = new MOrder(null, $this->onlineEnterpriseId);
        $this->objMInventoryOut = new MInventoryOut($this->onlineEnterpriseId, $this->onlineUserId);

        // 初始化分账明细模型
        $this->objMConsignmentSettlementDetail = new MConsignmentSettlementDetail($this->onlineEnterpriseId, $this->onlineUserId);



        // 初始化供应商代销配置模型
        $this->objMSupplierConsignment = new MSupplierConsignment($this->onlineEnterpriseId, $this->onlineUserId);
    }

    /**
     * 获取供应商代销配置
     */
    public function getConsignmentConfig()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        $supplierId = $params['supplierId'];

        $result = $this->objMSupplierConsignment->getConsignmentConfig($supplierId);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 更新供应商代销配置
     */
    public function updateConsignmentConfig()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        $supplierId = $params['supplierId'];

        // 构建配置数据
        $configData = [];

        if (isset($params['enabled'])) {
            $configData['enabled'] = $params['enabled'] ? true : false;
        }

        if (isset($params['settlementConfig'])) {
            $configData['settlementConfig'] = $params['settlementConfig'];
        }

        $result = $this->objMSupplierConsignment->updateConsignmentConfig($supplierId, $configData);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取供应商保证金账户余额
     */
    public function getDepositAccount()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        $supplierId = $params['supplierId'];

        $result = $this->objMSupplierDeposit->getDepositAccount($supplierId);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 充值保证金
     */
    public function depositFunds()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (empty($params['amount']) || !is_numeric($params['amount']) || $params['amount'] <= 0) {
            $this->sendOutput('充值金额必须大于0', ErrorCode::$paramError);
        }

        $supplierId = $params['supplierId'];
        $amount = floatval($params['amount']);
        $remark = $params['remark'] ?? '';

        $result = $this->objMSupplierDeposit->deposit($supplierId, $amount, $remark);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 提取保证金
     */
    public function withdrawFunds()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (empty($params['amount']) || !is_numeric($params['amount']) || $params['amount'] <= 0) {
            $this->sendOutput('提取金额必须大于0', ErrorCode::$paramError);
        }

        $supplierId = $params['supplierId'];
        $amount = floatval($params['amount']);
        $remark = $params['remark'] ?? '';

        $result = $this->objMSupplierDeposit->withdrawDeposit($supplierId, $amount, $remark);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 冻结保证金
     */
    public function freezeFunds()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (empty($params['amount']) || !is_numeric($params['amount']) || $params['amount'] <= 0) {
            $this->sendOutput('冻结金额必须大于0', ErrorCode::$paramError);
        }

        $supplierId = $params['supplierId'];
        $amount = floatval($params['amount']);
        $remark = $params['remark'] ?? '';

        $result = $this->objMSupplierDeposit->freeze($supplierId, $amount, $remark);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 解冻保证金
     */
    public function unfreezeFunds()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['supplierId'])) {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (empty($params['amount']) || !is_numeric($params['amount']) || $params['amount'] <= 0) {
            $this->sendOutput('解冻金额必须大于0', ErrorCode::$paramError);
        }

        $supplierId = $params['supplierId'];
        $amount = floatval($params['amount']);
        $remark = $params['remark'] ?? '';

        $result = $this->objMSupplierDeposit->unfreeze($supplierId, $amount, $remark);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取所有启用代销的供应商列表
     */
    public function getConsignmentSuppliers()
    {
        $params = $this->request->getRawJson();

        // 设置默认分页参数
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['pageSize']) ? intval($params['pageSize']) : 10;

        // 构建查询条件
        $conditions = [];

        // 关键词搜索
        if (!empty($params['keyword'])) {
            $conditions['keyword'] = $params['keyword'];
        }

        // 如果指定了供应商ID，添加到查询条件
        if (!empty($params['supplierId'])) {
            $conditions['supplierId'] = intval($params['supplierId']);
        }

        // 如果指定了代销状态，添加到查询条件
        if (isset($params['status']) && $params['status'] !== '') {
            $conditions['enabled'] = $params['status'] == '1' ? true : false;
        }

        // 调用模型获取代销供应商列表
        $result = $this->objMSupplierConsignment->getConsignmentSuppliers($conditions, $page, $pageSize);

        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageIndex' => $page,
                'pageSize' => $pageSize,
                'pageTotal' => $data['total'] ?? 0,
            ];
            $this->sendOutput($data['list'] ?? [], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取供应商代销状态统计
     */
    public function getConsignmentStats()
    {
        // 支持GET请求，不需要参数

        // 调用模型获取代销状态统计
        $result = $this->objMSupplierConsignment->getConsignmentStats();

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }





    /**
     * 10.6.3 获取代销出库单列表
     */
    public function getConsignmentOutbounds()
    {
        $params = $this->request->getRawJson();

        // 构建查询条件
        $conditions = [
            'type' => 'consignment' // 代销出库单类型
        ];

        // 出库单号
        if (!empty($params['outboundNo'])) {
            $conditions['outboundNo'] = $params['outboundNo'];
        }

        // 订单号
        if (!empty($params['orderNo'])) {
            $conditions['orderNo'] = $params['orderNo'];
        }

        // 供应商ID
        if (!empty($params['supplierId'])) {
            $conditions['supplierId'] = intval($params['supplierId']);
        }

        // 出库单状态
        if (isset($params['status']) && $params['status'] !== '') {
            $conditions['status'] = intval($params['status']);
        }

        // 时间范围 - 优先使用时间戳
        if (!empty($params['startTime']) && !empty($params['endTime'])) {
            // 处理前端传递的时间戳（秒）
            $startTime = intval($params['startTime']);
            $endTime = intval($params['endTime']);

            // 转换为日期字符串
            $startDate = date('Y-m-d H:i:s', $startTime);
            $endDate = date('Y-m-d H:i:s', $endTime);

            $conditions['timeRange'] = [
                'start' => $startDate,
                'end' => $endDate
            ];
        }
        // 兼容旧版接口
        else if (!empty($params['startDate']) && !empty($params['endDate'])) {
            $conditions['timeRange'] = [
                'start' => $params['startDate'] . ' 00:00:00',
                'end' => $params['endDate'] . ' 23:59:59'
            ];
        }

        // 功能未实现
        $this->sendOutput('该功能尚未实现', ErrorCode::$paramError);
    }

    /**
     * 10.6.4 获取代销出库单详情
     */
    public function getConsignmentOutboundDetail()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['outboundNo'])) {
            $this->sendOutput('出库单号不能为空', ErrorCode::$paramError);
        }

        // 功能未实现
        $this->sendOutput('该功能尚未实现', ErrorCode::$paramError);
    }



    /**
     * 10.6.5 审核代销出库单
     */
    public function auditConsignmentOutbound()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['outboundNo'])) {
            $this->sendOutput('出库单号不能为空', ErrorCode::$paramError);
        }

        // 功能未实现
        $this->sendOutput('该功能尚未实现', ErrorCode::$paramError);
    }

    /**
     * 10.6.6 取消代销出库单
     */
    public function cancelConsignmentOutbound()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['outboundNo'])) {
            $this->sendOutput('出库单号不能为空', ErrorCode::$paramError);
        }

        // 功能未实现
        $this->sendOutput('该功能尚未实现', ErrorCode::$paramError);
    }



    /**
     * 10.7.1 获取结算列表
     */
    public function getSettlementList()
    {
        $params = $this->request->getRawJson();

        // 设置默认分页参数
        $page = isset($params['page']) ? intval($params['page']) : 1;
        $pageSize = isset($params['pageSize']) ? intval($params['pageSize']) : 10;

        // 构建查询条件
        $conditions = [];

        // 结算单号
        if (!empty($params['settlementNo'])) {
            $conditions['settlementNo'] = $params['settlementNo'];
        }

        // 供应商ID
        if (!empty($params['supplierId'])) {
            $conditions['supplierId'] = intval($params['supplierId']);
        }

        // 结算状态
        if (isset($params['status']) && $params['status'] !== '') {
            $conditions['status'] = intval($params['status']);
        }

        // 时间范围
        if (!empty($params['startDate']) && !empty($params['endDate'])) {
            $conditions['timeRange'] = [
                'start' => $params['startDate'] . ' 00:00:00',
                'end' => $params['endDate'] . ' 23:59:59'
            ];
        }

        // 调用结算模型获取结算列表
        $result = $this->objMConsignmentSettlementDetail->getSettlementList($conditions, $page, $pageSize);

        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageIndex' => $page,
                'pageSize' => $pageSize,
                'pageTotal' => $data['total'] ?? 0,
            ];
            $this->sendOutput($data['list'] ?? [], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 10.7.2 获取结算详情
     */
    public function getSettlementDetail()
    {
        $params = $this->request->getRawJson();

        if (empty($params) || empty($params['settlementNo'])) {
            $this->sendOutput('结算单号不能为空', ErrorCode::$paramError);
        }

        $settlementNo = $params['settlementNo'];

        // 调用结算模型获取结算详情
        $result = $this->objMConsignmentSettlementDetail->getSettlementDetail($settlementNo);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 10.7.4 获取结算统计（增强版）
     */
    public function getSettlementStatistics()
    {
        $params = $this->request->getRawJson();

        // 构建查询条件
        $conditions = [];

        // 分页参数
        $conditions['page'] = isset($params['page']) ? intval($params['page']) : 1;
        $conditions['limit'] = isset($params['limit']) ? intval($params['limit']) : 10;

        // 供应商ID
        if (!empty($params['supplierId'])) {
            $conditions['supplierId'] = intval($params['supplierId']);
        }

        // 结算状态
        if (isset($params['settlementStatus']) && $params['settlementStatus'] !== '') {
            $conditions['settlementStatus'] = intval($params['settlementStatus']);
        }

        // 统计周期
        if (!empty($params['period'])) {
            $conditions['period'] = $params['period'];
        }

        // 时间范围（Unix时间戳）
        if (!empty($params['startDate'])) {
            $conditions['startDate'] = intval($params['startDate']);
        }
        if (!empty($params['endDate'])) {
            $conditions['endDate'] = intval($params['endDate']);
        }

        // 调用结算模型获取结算统计
        $result = $this->objMConsignmentSettlementDetail->getSettlementStatistics($conditions);

        if ($result->isSuccess()) {
            $data = $result->getData();

            // 构建分页信息
            $pageData = [
                'pageIndex' => $conditions['page'],
                'pageSize' => $conditions['limit'],
                'pageTotal' => $data['total'] ?? 0,
            ];

            // 返回数据结构
            $responseData = [
                'list' => $data['list'] ?? [],
                'total' => $data['total'] ?? 0,
                'summary' => $data['summary'] ?? [],
                'charts' => $data['charts'] ?? []
            ];

            $this->sendOutput($responseData, 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 10.7.5 获取扩展趋势分析数据
     */
    public function getExtendedTrendAnalysis()
    {
        $params = $this->request->getRawJson();

        // 构建查询条件
        $conditions = [];

        // 供应商ID
        if (isset($params['supplierId']) && !empty($params['supplierId'])) {
            $conditions['supplierId'] = intval($params['supplierId']);
        }

        // 结算状态
        if (isset($params['settlementStatus']) && $params['settlementStatus'] !== '') {
            $conditions['settlementStatus'] = intval($params['settlementStatus']);
        }

        // 统计周期
        if (isset($params['period']) && !empty($params['period'])) {
            $allowedPeriods = ['day', 'week', 'month', 'quarter', 'year'];
            if (in_array($params['period'], $allowedPeriods)) {
                $conditions['period'] = $params['period'];
            }
        }

        // 数据维度
        if (isset($params['dimension']) && !empty($params['dimension'])) {
            $allowedDimensions = ['amount', 'count', 'supplier', 'efficiency'];
            if (in_array($params['dimension'], $allowedDimensions)) {
                $conditions['dimension'] = $params['dimension'];
            }
        }

        // 时间范围
        if (isset($params['startDate']) && !empty($params['startDate'])) {
            $conditions['startDate'] = intval($params['startDate']);
        }

        if (isset($params['endDate']) && !empty($params['endDate'])) {
            $conditions['endDate'] = intval($params['endDate']);
        }

        // 数据条数限制
        if (isset($params['limit']) && !empty($params['limit'])) {
            $conditions['limit'] = intval($params['limit']);
        }

        // 调用模型获取扩展趋势分析数据
        $result = $this->objMConsignmentSettlementDetail->getExtendedTrendAnalysis($conditions);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 10.7.6 导出结算列表
     */
    public function exportSettlementList()
    {
        $params = $this->request->getRawJson();

        // 构建查询条件
        $conditions = [];

        // 结算单号
        if (!empty($params['settlementNo'])) {
            $conditions['settlementNo'] = $params['settlementNo'];
        }

        // 供应商ID
        if (!empty($params['supplierId'])) {
            $conditions['supplierId'] = intval($params['supplierId']);
        }

        // 结算状态
        if (isset($params['status']) && $params['status'] !== '') {
            $conditions['status'] = intval($params['status']);
        }

        // 时间范围
        if (!empty($params['startDate']) && !empty($params['endDate'])) {
            $conditions['timeRange'] = [
                'start' => $params['startDate'] . ' 00:00:00',
                'end' => $params['endDate'] . ' 23:59:59'
            ];
        }

        // 调用结算模型导出结算列表
        $result = $this->objMConsignmentSettlementDetail->exportSettlementList($conditions);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }



















    /**
     * 获取仓库商品列表
     */
    public function getWarehouseGoods()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $this->sendOutput('参数为空', ErrorCode::$paramError);
        }

        // 分页参数处理
        $pageParams = pageToOffset($params['page'] ?? 1, $params['pageSize'] ?? 10);
        $selectParams = [
            'limit' => $pageParams['limit'],
            'offset' => $pageParams['offset']
        ];

        // 添加查询条件
        if (!empty($params['supplierId'])) {
            $selectParams['supplierId'] = $params['supplierId'];
        } else {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (!empty($params['warehouseId'])) {
            $selectParams['warehouseId'] = $params['warehouseId'];
        } else {
            $this->sendOutput('仓库ID不能为空', ErrorCode::$paramError);
        }

        if (!empty($params['keyword'])) {
            $selectParams['search'] = $params['keyword'];
        }

        if (!empty($params['category'])) {
            $selectParams['categoryId'] = $params['category'];
        }

        if (!empty($params['goodsIds'])) {
            $selectParams['materielIds'] = explode(',', $params['goodsIds']);
        }

        if (!empty($params['skuIds'])) {
            $selectParams['skuIds'] = explode(',', $params['skuIds']);
        }

        // 调用库存模型获取仓库商品列表
        $objMInventory = new MInventory($this->onlineEnterpriseId, $this->onlineUserId);
        $result = $objMInventory->getInventoryByWarehouseId($selectParams);

        if ($result->isSuccess()) {
            $returnData = $result->getData();
            $pageData = [
                'pageIndex' => $params['page'] ?? 1,
                'pageSize' => $params['pageSize'] ?? 10,
                'pageTotal' => $returnData['total'] ?? 0,
            ];
            $this->sendOutput([
                'list' => $returnData['data'] ?? [],
                'total' => $returnData['total'] ?? 0
            ], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 获取供应商仓库商品列表
     * 只返回特定供应商在特定仓库的库存
     */
    public function getSupplierWarehouseGoods()
    {
        $params = $this->request->getRawJson();
        if (empty($params)) {
            $this->sendOutput('参数为空', ErrorCode::$paramError);
        }

        // 分页参数处理
        $pageParams = pageToOffset($params['page'] ?? 1, $params['pageSize'] ?? 10);
        $selectParams = [
            'limit' => $pageParams['limit'],
            'offset' => $pageParams['offset']
        ];

        // 添加查询条件
        if (!empty($params['supplierId'])) {
            $selectParams['supplierId'] = $params['supplierId'];
        } else {
            $this->sendOutput('供应商ID不能为空', ErrorCode::$paramError);
        }

        if (!empty($params['warehouseId'])) {
            $selectParams['warehouseId'] = $params['warehouseId'];
        } else {
            $this->sendOutput('仓库ID不能为空', ErrorCode::$paramError);
        }

        if (!empty($params['keyword'])) {
            $selectParams['keyword'] = $params['keyword'];
        }

        if (!empty($params['category'])) {
            $selectParams['categoryId'] = $params['category'];
        }

        if (!empty($params['goodsIds'])) {
            $selectParams['goodsIds'] = explode(',', $params['goodsIds']);
        }

        if (!empty($params['skuIds'])) {
            $selectParams['skuIds'] = explode(',', $params['skuIds']);
        }

        // 调用供应商库存模型获取供应商仓库商品列表
        $objMInventorySupplier = new MInventorySupplier($this->onlineEnterpriseId, $this->onlineUserId);
        $result = $objMInventorySupplier->getAllInventorySupplier($selectParams);

        if ($result->isSuccess()) {
            $returnData = $result->getData();
            $pageData = [
                'pageIndex' => $params['page'] ?? 1,
                'pageSize' => $params['pageSize'] ?? 10,
                'pageTotal' => $returnData['total'] ?? 0,
            ];
            $this->sendOutput([
                'list' => $returnData['data'] ?? [],
                'total' => $returnData['total'] ?? 0
            ], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    // 结算周期相关方法已迁移到 Finance/SettlementCycle.Class.php

    /**
     * 获取当前登录供应商ID（供应商角色端专用）
     *
     * @return int 供应商ID
     * @throws \Exception 当供应商未登录或无权限时抛出异常
     */
    protected function getCurrentSupplierId()
    {
        if (empty($this->supplierId)) {
            $this->sendOutput('供应商未登录或无权限', ErrorCode::$notAllowAccess);
        }
        return $this->supplierId;
    }

    /**
     * 验证供应商权限（供应商角色端专用）
     * 确保当前登录的供应商只能访问自己的数据
     *
     * @param int $targetSupplierId 目标供应商ID
     * @return bool 验证通过返回true
     * @throws \Exception 当权限验证失败时抛出异常
     */
    protected function validateSupplierPermission($targetSupplierId)
    {
        $currentSupplierId = $this->getCurrentSupplierId();

        if ($currentSupplierId != $targetSupplierId) {
            $this->sendOutput('无权限访问其他供应商数据', ErrorCode::$notAllowAccess);
        }

        return true;
    }

    /**
     * 供应商角色端概览数据（任务3专用）
     * 获取供应商基础统计信息，严格限制只返回仓储相关数据
     */
    public function getOverviewData()
    {
        $currentSupplierId = $this->getCurrentSupplierId();

        // 调用模型获取概览数据
        $result = $this->objMSupplierConsignment->getSupplierOverviewData($currentSupplierId);

        if ($result->isSuccess()) {
            $this->sendOutput($result->getData());
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }

    /**
     * 供应商角色端固定价格查询（任务7专用）
     * 从consignmentConfig字段中获取固定结算价格信息
     */
    public function getFixedPriceList()
    {
        $params = $this->request->getRawJson();
        $currentSupplierId = $this->getCurrentSupplierId();

        // 分页参数
        $page = pageToOffset($params['page'] ?? 1, $params['pageSize'] ?? 10);

        // 调用模型获取固定价格列表
        $result = $this->objMSupplierConsignment->getSupplierFixedPriceList($currentSupplierId, $page);

        if ($result->isSuccess()) {
            $data = $result->getData();
            $pageData = [
                'pageTotal' => $data['total'] ?? 0,
            ];
            $this->sendOutput($data['list'] ?? [], 0, $pageData);
        } else {
            $this->sendOutput($result->getData(), $result->getErrorCode());
        }
    }


}
