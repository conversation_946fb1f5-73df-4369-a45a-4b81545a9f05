<template>
  <Container>
    <div slot="right">
      <el-form size="small" :inline="true" :model="searchForm">
        <el-form-item>
          <el-input
            v-model="searchForm.keyword"
            style="width: 320px"
            placeholder="商品名称/SKU编码"
            clearable
            @keyup.enter.native="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-button slot="append" icon="el-icon-search" @click="pageChange(1)"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            style="width: 220px"
            placeholder="请选择状态"
            clearable
            @change="pageChange(1)"
            @clear="pageChange(1)"
          >
            <el-option label="生效中" value="active" />
            <el-option label="已失效" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="pageChange(1)">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      border
      element-loading-text="正在加载固定价格数据..."
      empty-text="暂无固定价格数据"
    >
      <el-table-column prop="goodsName" label="商品名称" min-width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <div class="goods-title">{{ scope.row.goodsName }}</div>
          <div v-if="scope.row.goodsCode" class="goods-no">{{ scope.row.goodsCode }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="sku" label="SKU编码" min-width="120" show-overflow-tooltip />
      <el-table-column prop="fixedPrice" label="固定结算价格" min-width="140" align="right">
        <template slot-scope="scope">
          <span class="price-text">¥{{ formatAmount(scope.row.fixedPrice) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="effectiveTime" label="生效时间" min-width="160" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.effectiveTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" min-width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'" size="small">
            {{ scope.row.status === "active" ? "生效中" : "已失效" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip />
      <el-table-column label="操作" width="120" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
          <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <FooterPage
      :page-size="pageSize"
      :total-page.sync="total"
      :current-page.sync="currentPage"
      @pageChange="pageChange"
      @sizeChange="sizeChange"
    />
  </Container>
</template>

<script>
import { getSupplierFixedPrices } from "@/api/SupplierStorage";
import FooterPage from "@/component/common/FooterPage";

export default {
  name: "SupplierStoragePrice",
  components: {
    FooterPage,
  },
  data() {
    return {
      loading: false,
      searchForm: {
        keyword: "",
        status: "",
      },
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
    };
  },
  created() {
    this.getData();
  },
  activated() {
    if (this.$_isInit()) return;
    this.getData();
  },
  methods: {
    // 分页改变
    pageChange(val) {
      this.currentPage = val;
      this.getData();
    },

    // 每页数据大小改变
    sizeChange(val) {
      this.pageSize = val;
      this.pageChange(1);
    },

    // 获取数据
    async getData() {
      this.loading = true;
      try {
        const params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          ...this.searchForm,
        };

        const response = await getSupplierFixedPrices(params);
        if (response.errorcode === 0) {
          this.tableData = response.data || [];
          this.total = response.pageTotal || 0;

          // 如果是搜索结果且无数据，给出提示
          if (this.tableData.length === 0 && (this.searchForm.keyword || this.searchForm.status)) {
            this.$message.info("未找到符合条件的固定价格数据");
          }
        } else {
          this.$message.error(response.message || "获取固定价格失败");
          this.tableData = [];
          this.total = 0;
        }
      } catch (error) {
        console.error("获取固定价格失败:", error);
        this.$message.error("网络错误，请稍后重试");
        this.tableData = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
    },

    // 重置搜索条件
    handleReset() {
      this.searchForm = {
        keyword: "",
        status: "",
      };
      this.pageChange(1);
    },

    // 格式化金额
    formatAmount(amount) {
      if (amount === null || amount === undefined || amount === "") {
        return "0.00";
      }
      return parseFloat(amount).toFixed(2);
    },

    // 格式化日期时间
    formatDateTime(timestamp) {
      if (!timestamp) return "";

      // 如果是时间戳（数字），转换为日期
      if (typeof timestamp === "number") {
        return this.$_common.formatDate(timestamp, "yyyy-MM-dd HH:mm:ss");
      }

      // 如果已经是格式化的字符串，直接返回
      return timestamp;
    },

    // 查看详情
    handleDetail(row) {
      this.$message.info(`查看商品 ${row.goodsName} 的价格详情`);
      // TODO: 实现详情查看功能
    },

    // 编辑价格
    handleEdit(row) {
      this.$message.info(`编辑商品 ${row.goodsName} 的固定价格`);
      // TODO: 实现编辑功能
    },
  },
};
</script>

<style lang="scss" scoped>
.price-text {
  font-weight: 600;
  color: #e6a23c;
  font-size: 14px;
}

.goods-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.goods-no {
  color: #909399;
  font-size: 12px;
}

.success-status {
  color: #67c23a;
  font-weight: 500;
}

.info-status {
  color: #909399;
  font-weight: 500;
}

// 表格操作按钮样式
.el-table {
  .el-button--text {
    padding: 0;
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 搜索表单样式
.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 15px;
}
</style>
